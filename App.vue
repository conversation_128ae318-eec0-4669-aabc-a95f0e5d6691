<script setup>
  import { onLaunch, onShow, onError } from '@dcloudio/uni-app';
  import { ShoproInit } from './sheep';

  onLaunch(() => {
    // 隐藏原生导航栏 使用自定义底部导航
    uni.hideTabBar({
      fail: () => {},
    });

    // 加载Shopro底层依赖
    ShoproInit();
    uni.loadFontFace({
				global: true,
				family: 'Source Han Serif',
				source: 'url("https://xsyczb.0rui.cn/uploads/ziti/SourceHanSerif.ttf")',
				success: function(res) {
					console.log('字体加载成功');
					console.log(res);
				}
			})
  });

  onError((err) => {
    console.log('AppOnError:', err);
  });

  onShow(() => {
    // #ifdef APP-PLUS
    // 获取urlSchemes参数
    const args = plus.runtime.arguments;
    if (args) {
    }

    // 获取剪贴板
    uni.getClipboardData({
      success: (res) => {},
    });
    // #endif
  });
</script>

<style lang="scss">
  @import '@/sheep/scss/index.scss';
  page{
    font-family: 'Source Han Serif'!important;
  }
  .noImg{
    width: 0;
    height: 0;
  }
</style>
