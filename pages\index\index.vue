<template>
	<view style="background-color: #190000;min-height: 100vh;padding-bottom: 150rpx;">
		<view v-if="!topShow"
			style="width: 100%;;display: flex;align-items: center;gap: 100rpx;top: 90rpx;height: 80rpx;line-height: 80rpx;position: fixed;z-index: 100;">
			<image class="noImg" src="/static/img/logo.png" style="width: 290rpx;" mode="widthFix"></image>
			<view style="display: flex;align-items: center;gap: 30rpx;">
				<image src="/static/img/top1.png" style="width: 60rpx;height: 60rpx;"></image>
				<image src="/static/img/top2.png" style="width: 60rpx;height: 60rpx;"></image>
			</view>
		</view>
		<view v-if="topShow"
			style="justify-content: center;text-align: center;background-color: #190000;width: 100%;;display: flex;align-items: center;gap: 100rpx;padding-top: 90rpx;height: 80rpx;line-height: 80rpx;position: fixed;z-index: 100;">
			<image class="noImg" src="/static/img/logo.png" style="width: 290rpx;" mode="widthFix"></image>
		</view>
		<view>
			<swiper class="swiper_s" :circular="true" :autoplay="true" indicator-active-color="#0DAE11"
				indicator-color="#ffffff" :indicator-dots="false">
				<swiper-item style="margin: 0 auto;">
					<view style="position: relative;width: 100%;height: 100%;">
						<image class="swiper-image" src="/static/img/banner.png" mode="scaleToFill" />
					</view>
				</swiper-item>
			</swiper>
		</view>
		<view style="display: flex;justify-content: center;margin-top: 30rpx;">
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/left.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
			<view>
				<text style="color: #FFFFFF;font-size: 36rpx;font-weight: 900;letter-spacing:15rpx">五行生扶系</text>
			</view>
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/right.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view style="margin-top: 50rpx;">
			<scroll-view scroll-x="true" class="goods-scroll-container" show-scrollbar="false">
				<view class="goods-list">
					<view class="goodsBg" v-for="item in 5" :key="item">
						<image src="https://dummyimage.com/400x400/ffffff/000"
							style="width: 210rpx;height: 210rpx;object-fit: cover;"></image>
						<view style="font-weight: 700;font-size: 28rpx;color: #F0DBC5;margin: 20rpx 0rpx;">火系水晶手串</view>
						<view style="font-weight: 700;font-size: 28rpx;color: #FFC07E;">￥699.00</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view style="display: flex;justify-content: center;margin-top: 50rpx;">
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/left.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
			<view>
				<text style="color: #FFFFFF;font-size: 36rpx;font-weight: 900;letter-spacing:15rpx">水晶十八籽</text>
			</view>
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/right.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view style="margin: 50rpx;">
			<view style="display: flex;">
				<view class="goodsTowBg">
					<view style="display: flex;align-items: center;justify-content: center;">
						<view>
							<view
								style="margin: 0 auto;text-align: center;width: 240rpx;font-weight: 900;font-size: 28rpx;color: #F0DBC5;">
								九紫离火天然多宝紫水晶十八籽</view>
							<view
								style="font-size: 28rpx;color: #F0DBC5;border-radius: 10rpx;font-weight: 300;margin: 0 auto;border: 1px solid #F0DBC5;width: 200rpx;height: 65rpx;line-height: 65rpx;text-align: center;margin-top: 30rpx;">
								查看详情</view>
						</view>
					</view>
				</view>
				<view style="width: 345rpx;height: 345rpx;">
					<image src="https://dummyimage.com/400x400/ffffff/000"
						style="width: 100%;height: 100%;object-fit: cover;border-radius: 0px 16rpx 0px 0px;">
					</image>
				</view>
			</view>
			<view style="display: flex;">
				<view style="width: 345rpx;height: 345rpx;">
					<image src="https://dummyimage.com/400x400/ffffff/000"
						style="width: 100%;height: 100%;object-fit: cover;border-radius: 0px 0rpx 0px 16rpx;">
					</image>
				</view>
				<view class="goodsTowBg2">
					<view style="display: flex;align-items: center;justify-content: center;">
						<view>
							<view
								style="margin: 0 auto;text-align: center;width: 240rpx;font-weight: 900;font-size: 28rpx;color: #F0DBC5;">
								九紫离火天然多宝紫水晶十八籽</view>
							<view
								style="font-size: 28rpx;color: #F0DBC5;border-radius: 10rpx;font-weight: 300;margin: 0 auto;border: 1px solid #F0DBC5;width: 200rpx;height: 65rpx;line-height: 65rpx;text-align: center;margin-top: 30rpx;">
								查看详情</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view style="display: flex;justify-content: center;margin-top: 50rpx;">
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/left.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
			<view>
				<text style="color: #FFFFFF;font-size: 36rpx;font-weight: 900;letter-spacing:15rpx">五行三圈系列</text>
			</view>
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/right.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view class="grid-container">
			<template v-for="(item, index) in 6" :key="index">
				<view class="grid-item" style="text-align: center;">
					<image src="https://dummyimage.com/400x400/ffffff/000"
						style="height: 150rpx;width: 150rpx;object-fit: cover;"></image>
					<view style="font-weight: 700;font-size: 28rpx;color: #F0DBC5;margin-top: 20rpx;">火系水晶手串</view>
				</view>
				<!-- 分隔线：只在不是每行最后一个且不是最后一个商品时显示 -->
				<view v-if="(index + 1) % 3 !== 0 && index < 5" class="grid-divider" style="width: 1rpx;height: 80rpx;background: #F0DBC5;"></view>
			</template>
		</view>
		<!-- 注册送优惠券列表 -->
		<!-- <su-popup :show="registerShow" type="center" round="10" :isMaskClick="false" backgroundColor="none"
				@close="registerShow = false" @open="registerShow = true">
				<view class="popup">
					<view class="registerBox">
						<view class="regListBox" v-for="(item, index) in registerList" :key="index">
							<view class="reItem">
								<view>
									<view style="font-size: 36rpx;font-weight: 900;color: #fff;line-height: 40rpx;">
										{{item.amount}}
									</view>
									<view style="font-size: 24rpx;color: #fff;line-height: 34rpx;margin-top: 10rpx;">
										{{ item.amount_text }}
									</view>
								</view>
								<view
									style="font-size: 26rpx;font-weight: bold;color: #fff;line-height: 36rpx;margin-left: 10rpx;">
									{{item.type_text}}
								</view>
							</view>
						</view>
					</view>

					<view class="texbut" @click="newReceiveCoupon()">

					</view>
					
					<view style="margin: 0 auto;position: absolute;bottom: -90rpx;" >
						<image style="width: 50rpx;height: 50rpx;" src="https://jiangxiaoxian.0rui.cn/registerCancel.png" 
						mode="aspectFill" @click="registerShow = false"></image>
					</view>
				</view>
			</su-popup> -->
		<s-tabbar path="/pages/index/index" />
	</view>
</template>

<script setup>
import {
	computed,
	ref,
	reactive,
} from 'vue';
import {
	onLoad,
	onShow,
	onPageScroll,
	onPullDownRefresh
} from '@dcloudio/uni-app';
import sheep from '@/sheep';
import $share from '@/sheep/platform/share';
import {
	consignee,
	mobile,
	address,
	region
} from '@/sheep/validate/form';

const topShow=ref(true);



function getAreaCity() {
	// if (_.isEmpty(uni.getStorageSync('areaCity'))) {
	sheep.$api.rent.getCity().then((res) => {
		if (res.code === 1) {
			uni.setStorageSync('areaCity', res.data);
		}
	});
	// }
};

function getTag() {
	// if (_.isEmpty(uni.getStorageSync('areaCity'))) {
	sheep.$api.rent.tagsList().then((res) => {
		if (res.code === 1) {
			uni.setStorageSync('tagList', res.data.list);
		}
	});
	// }
};

function getType() {
	// if (_.isEmpty(uni.getStorageSync('areaCity'))) {
	sheep.$api.rent.listType().then((res) => {
		if (res.code === 1) {
			uni.setStorageSync('typeList', res.data);
		}
	});
	// }
};


const template = computed(() => sheep.$store('app').template?.home);
const isLogin = computed(() => sheep.$store('user').isLogin);
const qualification = ref(false);
const registerShow = ref(false); //注册获取优惠券弹框
const registerList = ref([]);
const registerNum = ref(0);



const state = reactive({
	showRegion: false,
	model: {
		consignee: '',
		mobile: '',
		address: '',
		is_default: false,
		region: '',
	},
	rules: {
		consignee,
		mobile,
		address,
		region,
	},
});

const getAreaData = () => {
	if (_.isEmpty(uni.getStorageSync('areaData'))) {
		sheep.$api.data.area().then((res) => {
			if (res.code === 1) {
				uni.setStorageSync('areaData', res.data);
			}
		});
	}
};



const proCity = ref('')

const onRegionConfirm = (e) => {
	console.log('onRegionConfirm', e);

	state.model = {
		...state.model,
		...e,
	};

	if (state.model.province_name == state.model.city_name) {
		proCity.value = state.model.province_name
	} else {
		// proCity.value = state.model.province_name + " " + state.model.city_name
		proCity.value = state.model.province_name + state.model.city_name
	}

	// proCity.value = state.model.province_name + " " + state.model.city_name

	console.log('onRegionConfirm33', state.model, proCity.value);
	listQuery.value.province = state.model.province_id
	listQuery.value.city = state.model.city_id;
	listQuery.value.page = 1;
	rentList.value = [];
	getList()
	state.showRegion = false;
};

const listQuery = ref({
	page: 1,
	limit: 10,
	addrLatitude: null,
	addrLongitude: null,
	address: null,
	address1: null,
	categoryId: null,
	tags: '',
	cate_ids: '',
	area: null,
	city: null,
	province: null,
	district: null,
	keywords: null,

	// district: '',
})



onLoad((options) => {
	getAreaCity();
	getList();
	getBanner();

	// #ifdef MP
	// 小程序识别二维码
	if (options.scene) {
		const sceneParams = decodeURIComponent(options.scene).split('=');
		options[sceneParams[0]] = sceneParams[1];
	}
	// #endif

	// 预览模板
	if (options.templateId) {
		sheep.$store('app').init(options.templateId);
	}

	// 解析分享信息
	if (options.spm) {
		$share.decryptSpm(options.spm);
	}

	// 进入指定页面(完整页面路径)
	if (options.page) {
		sheep.$router.go(decodeURIComponent(options.page));
	}
});

onShow(() => {
	getScoreInfo();
})
function getScoreInfo() {
	sheep.$api.coupon.registerCoupon().then((res) => {
		if (res.code == 1) {
			console.log('res-registerCoupon:', res);
			registerList.value = res.data.data;
			registerNum.value = res.data.total
			if (registerNum.value != 0) {
				registerShow.value = true;
			} else {
				registerShow.value = false;
			}
			console.log('新用户', registerShow.value);
		} else {
			console.log('非新用户');
			sheep.$helper.toast(res.msg);
		}
	})
}

function newReceiveCoupon() {
	if (isLogin.value == true) {
		sheep.$api.coupon.registerCouponReceive().then((res) => {
			if (res.code == 1) {
				console.log('registerCouponReceive:', res);
				sheep.$helper.toast(res.msg);
				registerShow.value = false;
				setTimeout(function () {
					uni.switchTab({
						url: '/pages/index/category'
					})
				}, 500);
			} else {
				sheep.$helper.toast(res.msg);
			}
		})
	} else {
		sheep.$helper.toast('请先登录');
		// uni.showToast({
		//  title: '请先登录',
		//  icon: 'none',
		//  duration: 2000
		// })

		setTimeout(function () {
			uni.switchTab({
				url: '/pages/index/user'
			})
		}, 1000);

	}


}

//轮播图跳转
function swiperJump(item) {
	console.log('轮播图跳转事件：', item.url);
	const tabBarPages = [
		'/pages/index/index',
		'/pages/index/category',
		'/pages/index/user'
	];
	console.log('tabbarsList：', tabBarPages);
	if (item.type == "in") {
		console.log('跳进内页');
		if (tabBarPages.includes(item.url)) {
			console.log('导航页');
			uni.switchTab({
				url: item.url,
			})
		} else {
			uni.navigateTo({
				url: item.url,
			})
		}
	}
}

// 下拉刷新
onPullDownRefresh(() => {
	sheep.$store('app').init();
	setTimeout(function () {
		uni.stopPullDownRefresh();
	}, 800);
	rentList.value = [];
	state.model.city_name = '';
	state.model.province_name = '';
	state.currentCityIndex = [0, 0];
	getList();
});

onPageScroll(() => { });

const bannerList = ref([])
async function getBanner() {
	const res = await sheep.$api.home.homeBanner({});
	console.log('banner', res);
	if (res.code == 1) {
		bannerList.value = res.data.list
		console.log('bannerList', bannerList.value);
	}

}


const toPage = (e) => {
	if (isLogin.value == true) {
		uni.navigateTo({
			url: e
		})
	} else {
		sheep.$helper.toast('请先登录');
		// uni.showToast({
		// 	title: '请先登录',
		// 	icon: 'none',
		// 	duration: 2000
		// })

		setTimeout(function () {
			uni.switchTab({
				url: '/pages/index/user'
			})
		}, 2000);

	}
}

const toTabb = (e) => {
	uni.switchTab({
		url: e,
	})
}

const rentList = ref([]);
const rentCount = ref(0);
const homrS = ref(false)
//招租列表
async function getList() {
	const res = await sheep.$api.rent.rentlist({
		keywords: listQuery.value.keywords,
		page: listQuery.value.page,
		limit: 10,
		cate_ids: listQuery.value.cate_ids,
		order: 'normal',
		status: 1,
		recommend: 1,
		province: listQuery.value.province,
		city: listQuery.value.city,

	});
	console.log('getList', res);

	if (res.data && res.data.list) {
		// rentList.value = res.data.list;
		// rentCount.value = res.data.count
		rentList.value = [...rentList.value, ...res.data.list];
		rentCount.value = res.data.count;

		// Safely process each item
		rentList.value = rentList.value.map(item => {
			// Create a new object with all properties from the original item
			const newItem = {
				...item
			};

			// Only process release_time_text if it exists
			if (newItem.release_time_text) {
				newItem.release_time_text = newItem.release_time_text.substring(0, 10);
			}

			return newItem;
		});
	} else {
		// Handle case where data is not in expected format
		rentList.value = [];
	}

	// rentList.value = res.data.list;
	// for(let i=0;i<rentList.value.length;i++){
	// 	rentList.value[i].release_time_text = rentList.value[i].release_time_text.substring(0, 10)
	// }
	console.log('getList', rentList.value);
}

//加载更多
function onScrolltolower() {
	if (rentList.value.length < rentCount.value) {
		listQuery.value.page += 1;
		getList();
	}
}
//下拉刷新
function onS() {
	homrS.value = true
	listQuery.value.keywords = '';
	listQuery.value.cate_ids = '';
	listQuery.value.province = '';
	state.model.city_name = '';
	listQuery.value.city = '';
	state.currentCityIndex = [0, 0];
	uni.showLoading({
		title: '加载中...'
	});
	resetLists();

	setTimeout(() => {
		homrS.value = false;
		uni.hideLoading();
		uni.stopPullDownRefresh();
	}, 2000)
}
// 重置列表
function resetLists() {
	// listQuery.value.keywords = ''
	console.log('listQuery.value.keywords', listQuery.value.keywords);
	listQuery.value.page = 1;
	rentList.value = [];
	getList();
}

// 隐藏原生tabBar
uni.hideTabBar({
	fail: () => { },
});
</script>

<style lang="scss" scoped>
/* 对于没有安全区域的设备，设置默认值 */
@supports not (padding-bottom: env(safe-area-inset-bottom)) {
	.scrollBox {
		padding-bottom: 0;
	}
}

.swiper-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.swiper_s {
	height: 1200rpx;
}

/* 横向滚动容器样式 */
.goods-scroll-container {
	width: 100%;
	height: 400rpx;
	white-space: nowrap;
}

.goods-list {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	padding: 0 30rpx;
	gap: 30rpx;
}

/* Grid布局样式 */
.grid-container {
	display: grid;
	grid-template-columns: 1fr auto 1fr auto 1fr;
	gap: 0;
	align-items: center;
	padding: 0 30rpx;
	margin-top: 30rpx;
}

.grid-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.grid-divider {
	justify-self: center;
	align-self: center;
}

.goodsBg {
	background-image: url("https://xsyczb.0rui.cn/my_img/goodsBg.png");
	background-size: 100% 100%;
	text-align: center;
	width: 240rpx;
	height: 360rpx;
	padding: 10rpx 15rpx;
	flex-shrink: 0;
	/* 防止商品卡片被压缩 */
}

.goodsTowBg {
	background-image: url("https://xsyczb.0rui.cn/my_img/left_top.png");
	background-size: 100% 100%;
	width: 345rpx;
	height: 345rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.goodsTowBg2 {
	background-image: url("https://xsyczb.0rui.cn/my_img/bottom_right.png");
	background-size: 100% 100%;
	width: 345rpx;
	height: 345rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}
</style>